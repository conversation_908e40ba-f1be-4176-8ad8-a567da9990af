# Trustay API Actions

Th<PERSON> mục này chứa các action để kết nối với Trustay backend API.

## Cấu trúc

- `api-client.ts` - API client với authentication và error handling
- `types.ts` - TypeScript types cho API requests/responses
- `auth-actions.ts` - Authentication actions (login, register, verify, etc.)
- `user-actions.ts` - User management actions (profile, update)
- `location-actions.ts` - Location services (provinces, districts, wards)
- `index.ts` - Export tất cả actions

## Cấu hình

Tạo file `.env.local` với:

```env
NEXT_PUBLIC_API_URL=http://localhost:3000
NODE_ENV=development
```

## Sử dụng

### Authentication

```typescript
import { login, registerDirect, logout } from '@/actions'

// Login
const authResponse = await login({ email, password })

// Register (development mode)
const authResponse = await registerDirect({
  email,
  password,
  firstName,
  lastName,
  phone,
  gender: 'male',
  role: 'tenant'
})

// Logout
await logout()
```

### User Management

```typescript
import { getUserProfile, updateUserProfile } from '@/actions'

// Get profile
const profile = await getUserProfile()

// Update profile
const updatedProfile = await updateUserProfile({
  firstName: 'New Name',
  bio: 'Updated bio'
})
```

### Location Services

```typescript
import { getProvinces, getDistrictsByProvince, getWardsByDistrict } from '@/actions'

// Get all provinces
const provinces = await getProvinces()

// Get districts by province
const districts = await getDistrictsByProvince('79') // Ho Chi Minh City

// Get wards by district
const wards = await getWardsByDistrict('760') // District 1
```

## Error Handling

Tất cả actions throw `ApiError` khi có lỗi:

```typescript
import { ApiError } from '@/actions'

try {
  await login({ email, password })
} catch (error) {
  if (error instanceof ApiError) {
    console.error('API Error:', error.message, error.status)
  }
}
```

## Token Management

API client tự động:
- Thêm Authorization header cho protected endpoints
- Refresh token khi expired
- Clear tokens khi logout
- Redirect về login khi unauthorized

## User Store Integration

User store đã được cập nhật để sử dụng API:

```typescript
import { useUserStore } from '@/stores/user-store'

const { user, isAuthenticated, login, logout, loadUser } = useUserStore()

// Login với API
await login({ email, password })

// Load user từ token
await loadUser()

// Logout
await logout()
```
