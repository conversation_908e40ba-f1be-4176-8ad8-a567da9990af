// User management actions for Trustay API
import { apiClient, ApiError } from './api-client';
import { UserProfile, UpdateProfileRequest } from './types';

// Get user profile
export const getUserProfile = async (): Promise<UserProfile> => {
  try {
    const response = await apiClient.get<UserProfile>('/api/users/profile');
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new ApiError('Failed to get user profile', 400);
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Network error while getting user profile', 0);
  }
};

// Update user profile
export const updateUserProfile = async (
  profileData: UpdateProfileRequest
): Promise<UserProfile> => {
  try {
    const response = await apiClient.put<UserProfile>('/api/users/profile', profileData);
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new ApiError('Failed to update user profile', 400);
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Network error while updating user profile', 0);
  }
};
