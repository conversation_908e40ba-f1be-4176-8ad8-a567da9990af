"use client"

import { useEffect } from 'react'
import { useUserStore } from '@/stores/user-store'

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { loadUser, isAuthenticated } = useUserStore()

  useEffect(() => {
    // Only load user if not already authenticated
    if (!isAuthenticated) {
      loadUser()
    }
  }, [loadUser, isAuthenticated])

  return <>{children}</>
}
